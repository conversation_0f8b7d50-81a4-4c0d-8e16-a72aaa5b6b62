#服务端配置参数[新增或者删除配置项参数项时,需要同步修改代码:constants.SERVER_REQUIRED_KEYS/ServerProperties._check_reset_server_config/test_server_properties整个代码中的所有函数]
# 需要同步配置到 other_conf中的配置文件中
[server]
# 第三方 获取未读信息的 API 密钥
api_key=vZMnVhJzW3Y5ejt6Yoyhm
# 所允许的第三方获取未读信息的 IP 白名单 [如果存在多个值，用逗号分隔]
whitelist=127.0.0.1,*
# 数据文件路径 [可以是相对路径或绝对路径,多进程唯一]
message_data_path=D:/Git/python-samples-hub/data/messages.db
log_config_path=D:/Git/python-samples-hub/resources/log.ini
# IP访问限制由白名单和token控制
host=0.0.0.0
port=8000
# 运行时间段:如果开始时间和结束时间一致则表示全天运行
run_time=07:00-07:00
time_zone=Asia/Shanghai
# 已读数据过期时间（天） 非正数表示永不过期
expire_data_days=5
# 最大数据条数限制:超限则删除超限部分的已读数据 非正数表示不限制
data_limit_num=100000
# 当前启动服务的应用名称 [如果启动多个进程,这个名称需要保持唯一] 目前作为日志文件名前缀使用
app_name=webhook_server
enable_sql_logging=True
# 发信方标识及其说明
[client_info]
xascxadsas5c:测试设备1
kxnjhJhGFcgf:测试设备2
