# 使用SQLite来保证对应服务端中流转的数据的完整性和安全性,以下是对应的操作函数[存储到数据库中的时间字段是默认的UTC,但是给其他查看时会转换成自己设置的时区的时间]:
import logging
import sqlite3
from typing import Optional, List
from zoneinfo import ZoneInfo

import ulid

from config import constants
from models import server_data
from models.sqlite_base_manager import SQLiteBaseManager
from utils import server_utils

logger = logging.getLogger(__name__)

class WebhookDataManager(SQLiteBaseManager):
    """
    注意事项:
        1. 本表中reception_time字段需要转换成对应时区格式才能给外部使用
    """
    def __init__(self, db_path: str, zone: ZoneInfo = None, enable_sql_logging=False):
        logger.info(f"initializing webhook data manager with db_path: {db_path}")
        resolved_path = server_utils.get_real_path_create_dir(db_path)
        logger.info(f"resolved database path: {resolved_path}")

        super().__init__(db_path=resolved_path, enable_sql_logging=enable_sql_logging,zone=zone)
        logger.info("sqlite base manager initialized")


        logger.info(f"timezone set to: {self.zone}")
        logger.info("webhook data manager initialization completed")

    # 初始化数据库表结构并设置WAL/NORMAL模式
    def _init_db(self):
        # 初始化数据库:[新建表,创建索引,设置WAL/NORMAL模式]
        # , factory=sql_log.LoggingConnection --- 打印具体执行的SQL语句
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("PRAGMA journal_mode=WAL;")
            conn.execute("PRAGMA synchronous=NORMAL;")
            init_sql = """
                CREATE TABLE IF NOT EXISTS messages (
                    id TEXT NOT NULL PRIMARY KEY,
                    message TEXT NOT NULL CHECK(length(message) BETWEEN {min_message_len} AND {max_message_len}),
                    client_key TEXT NOT NULL CHECK(length(client_key) BETWEEN {min_client_key_len} AND {max_client_key_len}),
                    reception_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    is_read INTEGER NOT NULL DEFAULT 0 CHECK(is_read IN (0, 1))
                );
                CREATE INDEX IF NOT EXISTS idx_read_clientkey ON messages(is_read, client_key);
                CREATE INDEX IF NOT EXISTS idx_time ON messages(reception_time);
                CREATE INDEX IF NOT EXISTS idx_read_time ON messages(is_read, reception_time);
                CREATE INDEX IF NOT EXISTS idx_client_read_time ON messages(client_key, is_read, reception_time);
                """.format(min_message_len=constants.MIN_MESSAGE_LEN, max_message_len=constants.MAX_MESSAGE_LEN,
                           min_client_key_len=constants.MIN_CLIENT_KEY_LEN,
                           max_client_key_len=constants.MAX_CLIENT_KEY_LEN)
            conn.executescript(init_sql)
            conn.commit()


    # 保存接收到的消息
    def save_message(self, message: str, client_key: str) -> str:
        logger.info(f"saving message from client_key: {client_key}")
        logger.info(f"original message length: {len(message) if message else 0}")

        # 消息长度校验在事务开始前完成
        message = server_utils.trim(message)

        if message is None or not (constants.MIN_MESSAGE_LEN <= len(message) <= constants.MAX_MESSAGE_LEN):
            raise ValueError(
                f"the effective length range of the received data must be between {constants.MIN_MESSAGE_LEN}-{constants.MAX_MESSAGE_LEN} characters!")

        logger.info("message length validation passed")
        # 不需要校验client_key,已经在加载配置属性时就已经进行了校验
        # 为了保证重试时不会重复插入,这里必须保证幂等性
        message_id = ulid.new().str
        logger.info(f"generated message id: {message_id}")

        logger.info("executing database insert")
        self._exec_retry(lambda cur: cur.execute("INSERT INTO messages (id,message, client_key) VALUES (?,?, ?);",
                                                 (message_id, message, client_key)))
        logger.info(f"message saved successfully with id: {message_id}")
        return message_id



    # 读取消息[通用]并将将对应的数据标记为已读[内部函数:不需要参数校验,其中`query`对应的sql语句包含了limit参数] 由于在lambda中使用保持静态方法的使用,所以需要将`cur_zone`作为参数传入
    @staticmethod
    def _execute_read(cursor: sqlite3.Cursor, cur_zone: ZoneInfo, query_sql: str, params: tuple) -> List[server_data.MessageResponse]:
        cursor.execute(query_sql, params)
        rows = cursor.fetchall()

        if not rows:
            logger.warning(f"no unread data received for query: {query_sql}, params: {params}")
            return []

        ids = [row[0] for row in rows]
        for i in range(0, len(ids), constants.SQLITE_VARIABLE_LIMIT):
            had_read_ids = ids[i:i + constants.SQLITE_VARIABLE_LIMIT]
            placeholders = ','.join('?' * len(had_read_ids))
            cursor.execute(f"UPDATE messages SET is_read = 1 WHERE id IN ({placeholders}) AND is_read = 0;",
                           had_read_ids)

        # 其中将数据库默认的UTC时区转换成自己的时区
        return [server_data.MessageResponse(id=row[0], message=row[1], client_key=row[2],
                                            reception_time=WebhookDataManager.convert_utc_str(row[3], cur_zone),is_read=1) for row in
                rows]

    # 读取消息传递的参数的校验[内部函数] size:一次能够读取的消息数量,client_key:对应发送方的标识符
    @staticmethod
    def _check_read_msg_params(client_key: str|None, size: int) -> str|None:
        if not (constants.MIN_UNREAD_MSGS_LEN <= size <= constants.MAX_UNREAD_MSGS_LEN):
            raise ValueError(
                f"the range of unread data allowed to be read at once must be {constants.MIN_UNREAD_MSGS_LEN}-{constants.MAX_UNREAD_MSGS_LEN}")
        return server_utils.trim(client_key)

    # 获取最新的size条消息:包含已读和未读的消息 --- 给服务端gui界面显示使用【不能给客户端使用】
    def get_recent_data(self, size: int) -> List[server_data.MessageResponse]:
        logger.info(f"getting recent data with size: {size}")
        WebhookDataManager._check_read_msg_params(None, size)
        logger.info("parameters validated for recent data query")

        query = """
            SELECT  id,message, client_key, reception_time, is_read
            FROM messages
            ORDER BY reception_time DESC
            LIMIT ?;"""
        result = self._exec_retry(lambda cur: cur.execute(query, (size,)).fetchall())
        if not result:
            return []
        
        # 转换 reception_time 字段为对应时区格式
        result = [server_data.MessageResponse(id=row[0], message=row[1], client_key=row[2],
                                                  reception_time=WebhookDataManager.convert_utc_str(row[3], self.zone),is_read=row[4]) for
                      row in result]
        logger.info(f"retrieved {len(result)} recent data records")
        return result
    # 获取最旧的未读size条消息:[不需要进行client_key的长度校验,其client_key出现错误时就不会得到结果]
    def get_oldest_unread(self, size: int, client_key: Optional[str] = None) -> List[server_data.MessageResponse]:
        client_key = WebhookDataManager._check_read_msg_params(client_key, size)
        query = """
            SELECT id, message, client_key, reception_time 
            FROM messages 
            WHERE is_read = 0 
            AND (client_key = ? OR ? IS NULL)
            ORDER BY reception_time ASC 
            LIMIT ?;"""
        return self._exec_retry(
            lambda cur: WebhookDataManager._execute_read(cur, self.zone, query, (client_key, client_key, size)))

    # 获取最新minutes分钟内的未读size条消息
    def get_recent_unread(self, size: int, minutes: int, client_key: Optional[str] = None) -> List[server_data.MessageResponse]:
        logger.info(f"getting recent unread messages: size={size}, minutes={minutes}, client_key={client_key}")

        if not (constants.EARLIEST_RECENTLY_UNREAD_TIME <= minutes <= constants.LATEST_RECENTLY_UNREAD_TIME):
            logger.error(f"invalid minutes parameter: {minutes}, must be between {constants.EARLIEST_RECENTLY_UNREAD_TIME}-{constants.LATEST_RECENTLY_UNREAD_TIME}")
            raise ValueError(
                f"when obtaining unread data within the specified recent time, the specified time must be between {constants.EARLIEST_RECENTLY_UNREAD_TIME}-{constants.LATEST_RECENTLY_UNREAD_TIME} minutes")

        logger.info("minutes parameter validation passed")
        client_key = WebhookDataManager._check_read_msg_params(client_key, size)
        logger.info(f"validated client_key: {client_key}")

        query = """
            SELECT id, message, client_key, reception_time
            FROM messages
            WHERE is_read = 0
            AND (client_key = ? OR ? IS NULL)
            AND reception_time >= datetime('now', '-' || ? || ' minutes')
            ORDER BY reception_time DESC
            LIMIT ?;"""
        logger.info("executing recent unread query")
        result = self._exec_retry(
            lambda cur: WebhookDataManager._execute_read(cur, self.zone, query, (client_key, client_key, minutes, size)))
        logger.info(f"retrieved {len(result)} recent unread messages")
        return result

    # 获取超过[days]天数的未读消息数量
    def get_older_unread_count(self, days: int) -> int:
        if days < 1:
            raise ValueError("the specified days must be greater than 0")
        query = """
            SELECT COUNT(*) 
            FROM messages 
            WHERE is_read = 0 
            AND reception_time <= datetime('now', '-' || ? || ' days');"""
        return self._exec_retry(lambda cur: cur.execute(query, (days,)).fetchone()[0])

    # 删除过期expire_data_days天的已读数据
    @staticmethod
    def _remove_expired_read_data(cursor: sqlite3.Cursor, expire_data_days: int):
        while True:
            cursor.execute("""
                    DELETE FROM messages where id in (
                        SELECT id FROM messages  WHERE is_read = 1  AND date(reception_time) < date('now', '-' || ? || ' days') LIMIT 1000
                        )
                    ;""", (expire_data_days,))
            if cursor.rowcount == 0:
                break
            else:
                logger.info(f"deleted {cursor.rowcount} expired read data records")

    def remove_expired_read_data(self, expire_data_days):
        # 当所允许的数据库中过期时间是非正数时,则数据永不过期
        if expire_data_days < 1:
            logger.warning(f"the specified expire data days is less than 1, no expired data will be removed")
            return
        self._exec_retry(lambda c: WebhookDataManager._remove_expired_read_data(c, expire_data_days))

    # 删除超出限制的已读数据
    @staticmethod
    def _remove_excess_read_data(cursor: sqlite3.Cursor, data_limit_num: int):
        cursor.execute("""SELECT id FROM messages ORDER BY reception_time DESC LIMIT ?;""", (data_limit_num,))
        rows = cursor.fetchall()
        if not rows:
            return
        ids = [row[0] for row in rows]
        placeholders = ','.join('?' * len(ids))
        while True:
            cursor.execute(
                f"DELETE FROM messages WHERE id in (select id from messages WHERE is_read = 1 AND id not in ({placeholders}) LIMIT 1000);",
                ids)
            if cursor.rowcount == 0:
                break
            else:
                logger.info(f"deleted {cursor.rowcount} excess read data records")

    def remove_excess_read_data(self, data_limit_num: int):
        # 当所允许的数据库中条数是非正数时,则不限制数据数量
        if data_limit_num < 1:
            logger.warning(f"the specified data limit number is less than 1, no excess data will be removed")
            return
        if data_limit_num >= constants.MAX_MESSAGE_COUNT:
            data_limit_num = constants.MAX_MESSAGE_COUNT - 1
        logger.warning(f"the specified data limit number actually is {data_limit_num}")
        self._exec_retry(lambda cur: WebhookDataManager._remove_excess_read_data(cur, data_limit_num))
