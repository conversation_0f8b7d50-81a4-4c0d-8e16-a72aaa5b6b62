# 以类的方式定义一个WebhookServer,不同的实例间相互独立

import argparse
import asyncio
import functools
import logging
import multiprocessing
import os
import sys
import time
from datetime import datetime
from typing import Optional
from uuid import uuid4
from zoneinfo import ZoneInfo

import uvicorn
from fastapi import FastAPI, Header, HTTPException, status, Query, Depends, Request
from fastapi.responses import JSONResponse
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from typing_extensions import Annotated

from utils.runtime_webhook_server_config_lock import RuntimeWebhookConfigManager

src_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if src_path not in sys.path:
    sys.path.append(src_path)
from config import constants
from models import server_data, server_properties, server_state
from utils import server_utils, shutdown_exec_funct as shutdown_exec, file_lock, self_log


# ------------------ WebhookServer 类封装 ------------------
class WebhookServer:
    # 当前进程中实例化的webhook服务端实例中所涉及到的数据库文件 --- 同一个进程下多个实例不能使用同一个数据库文件
    cur_process_message_data_path = set()
    def __init__(self):
        self.logger: Optional[logging.Logger] = logging.getLogger(__name__) if self_log.log_config.had_init() else None
        server_utils.logger_print(msg="initializing webhook server instance", custom_logger=self.logger)
        self.app = FastAPI()
        server_utils.logger_print(msg="fastapi app created", custom_logger=self.logger)
        self.security = HTTPBearer()
        server_utils.logger_print(msg="http bearer security created", custom_logger=self.logger)
        self.web_server_state = server_state.ServerState()
        server_utils.logger_print(msg="server state created", custom_logger=self.logger)
        self._create_routes(self.security)
        server_utils.logger_print(msg="routes created successfully", custom_logger=self.logger)
        server_utils.logger_print(msg="webhook server instance initialization completed", custom_logger=self.logger)

    @staticmethod
    def _had_same_port(host_ports:dict[str,int],port:str):
        """在当前进程使用0.0.0.0时,检测是否有其他进程使用了相同的端口号"""
        for host_port in host_ports.keys():
            if host_port.split('_')[1]==port:
                return True
        return False
    # 不同的webhook服务端实例中关键配置项不能重复,一旦重复则禁止启动
    def _check_config_unique_keys(self)->bool:
        """"
        检测启动webhook服务端实例的关键配置项是否重复
        不论是同一个进程不同实例还是不同进程,都需要保证关键配置项不能重复
        目前校验项:message_data_path,host_port
        :return: bool True: 唯一, False: 重复(禁止启动)
        """
        config = self.web_server_state.properties.server_config
        cur_message_data_path = config["message_data_path"]
        host = config["host"]
        port = config["port"]
        self.logger.info(f"checking config uniqueness for message_data_path:{cur_message_data_path}, host:{host}, port:{port}")
        runtime_unique_config_manager=RuntimeWebhookConfigManager.get_singleton_instance(db_path=config["message_data_path"], enable_sql_logging=bool(config["enable_sql_logging"]), zone=ZoneInfo(config["time_zone"]))
        
        # 不同进程键相同配置项的值不能相同 DIFF_INSTANCE_UNIQUE_KEYS
        for unique_key in constants.DIFF_INSTANCE_UNIQUE_KEYS:
            # 存在重复配置项 包含多进程相同key-value,单进程多实例重复key检验
            config_value = config[unique_key]
            self.logger.debug(f"checking uniqueness for key:{unique_key}, value:{config_value}")
            if not runtime_unique_config_manager.register_config(unique_key, config_value):
                self.logger.error(f"this {unique_key}:{config_value} has been used by other process, this {unique_key} key 's value must be use another value!")
                return False
            self.logger.debug(f"uniqueness check passed for key:{unique_key}")
            
        host_port_key="host_port"
        cur_host_port_value=f"{host}_{port}"
        self.logger.debug(f"checking host_port uniqueness for value:{cur_host_port_value}")
        host_ports=runtime_unique_config_manager.get_valid_values_for_key(host_port_key)
        self.logger.debug(f"existing host_ports: {host_ports}")
        
        if host_ports and len(host_ports)>0:
            if cur_host_port_value in host_ports:
                self.logger.error(f"host:{host} and port:{port} has been used by other process, please use another host and port!")
                return False

            # 一旦存在有进程使用了 0.0.0.0,则当前进程的端口号就必须不能是相同的
            if f"0.0.0.0_{port}" in host_ports:
                self.logger.error(f"host:0.0.0.0 and port:{port} has been used by other process, please use another port!")
                return False
            # 如果是当前进程使用 0.0.0.0,则不能和已有进程的端口号不能是相同
            if "0.0.0.0" == host and WebhookServer._had_same_port(host_ports,str(port)):
                self.logger.error(f"this process use 0.0.0.0 and port:{port} has been used by other process, please use another port!")
                return False
                
        self.logger.debug(f"registering host_port key:{host_port_key}, value:{cur_host_port_value}")
        assert runtime_unique_config_manager.register_config(host_port_key, cur_host_port_value)
        
        try:
            self.logger.debug(f"checking if file is locked: {cur_message_data_path}")
            if file_lock.is_file_locked(cur_message_data_path):
                self.logger.error(f"message_data_path:{cur_message_data_path} is locked by other process, please use another message_data_path!")
                return False
            self.logger.debug(f"creating file lock for: {cur_message_data_path}")
            file_lock.FileLock(file_path=cur_message_data_path)
        except Exception: # noqa
            self.logger.exception(f"message_data_path:{cur_message_data_path} cannot be locked!")
            return False
            
        if cur_message_data_path in WebhookServer.cur_process_message_data_path:
            self.logger.error(f"message_data_path:{cur_message_data_path} has been used by other instance, please use another message_data_path!")
            return False

        self.logger.debug(f"adding message_data_path to current process set: {cur_message_data_path}")
        WebhookServer.cur_process_message_data_path.add(cur_message_data_path)
        
        self.logger.debug(f"checking if host:{host} and port:{port} is already in use")
        if server_utils.host_port_connect(host, port):
            self.logger.error(f"host:{config['host']} and port:{config['port']} has been used by other process, please use another host and port!")
            return False

        self.logger.info(f"all config uniqueness checks passed successfully")
        return True

    def _create_routes(self,security:HTTPBearer):
        server_utils.logger_print(msg="creating routes for webhook server", custom_logger=self.logger)
        # 使用闭包解决self引用问题
        async def token_wrapper(request: Request, credentials: Annotated[HTTPAuthorizationCredentials , Depends(security)]):
            api_key = credentials.credentials
            return await self.get_server_token(request, api_key)

        async def save_wrapper(send_data: server_data.SendData, request: Request,x_client_key: Annotated[Optional[str], Header()] = None):
            return await self.save_message(send_data, request, x_client_key)

        async def unread_wrapper(request: Request,size: Annotated[int, Query(gt=0)],credentials: Annotated[HTTPAuthorizationCredentials , Depends(security)],client_key: Annotated[str|None, Query()] = None):
            token = credentials.credentials
            return await self.get_client_unread(request, size, token, client_key)

        server_utils.logger_print(msg="registering api routes", custom_logger=self.logger)
        # 注册路由
        self.app.get("/webhook/token")(token_wrapper)
        server_utils.logger_print(msg="registered get /webhook/token route", custom_logger=self.logger)
        self.app.post("/webhook/save")(save_wrapper)
        server_utils.logger_print(msg="registered post /webhook/save route", custom_logger=self.logger)
        self.app.get("/webhook/unread")(unread_wrapper)
        server_utils.logger_print(msg="registered get /webhook/unread route", custom_logger=self.logger)

        server_utils.logger_print(msg="registering middleware", custom_logger=self.logger)
        # 注册中间件
        self.app.middleware("http")(self.check_ip_whitelist_log_requests)
        server_utils.logger_print(msg="registered ip whitelist middleware", custom_logger=self.logger)
        server_utils.logger_print(msg="routes creation completed", custom_logger=self.logger)


    # 检查IP白名单 --- request 请求log
    async def check_ip_whitelist_log_requests(self, request: Request, call_next):
        if not request.client or not request.client.host:
            self.logger.error("invalid client connection")
            return JSONResponse({"error": "invalid client"}, status_code=400)
            
        client_ip = request.client.host
        whitelist = self.web_server_state.properties.server_config["whitelist"]
        self.logger.debug(f"checking ip whitelist for client: {client_ip}, path: {request.url.path}")
        self.logger.debug(f"current whitelist: {whitelist}")
        
        if (whitelist[0] != "*" and request.url.path in constants.IP_CHECK_API_LIST
                and not server_utils.is_ip_in_whitelist(client_ip,whitelist)):
            self.logger.error(f"this ip:{client_ip} not allow request!")
            return JSONResponse({"error": "IP not allowed"}, status_code=403)
            
        start_time = time.time()
        self.logger.debug(f"processing request from {client_ip} to {request.url.path}")
        response = await call_next(request)
        process_time = (time.time() - start_time) * 1000
        self.logger.info(f"{client_ip} - {request.method} {request.url.path} - {response.status_code} - {process_time:.2f}ms")
        return response

    # 根据api_key获取当前有效的token
    async def get_server_token(self, request: Request, api_key: str):
        self.logger.debug(f"token request received from {request.client.host}, validating api_key")
        if api_key != self.web_server_state.properties.server_config["api_key"]:
            self.logger.error(f"this request api key invalid from {request.client.host}!")
            raise HTTPException(status.HTTP_403_FORBIDDEN)
        self.logger.info(f"{request.client.host} get server token")
        return {"token": self.web_server_state.server_refresh_token}

    # 接收发信方消息
    async def save_message(self, send_data: server_data.SendData, request: Request,
                           x_client_key: Annotated[Optional[str], Header()] = None):
        self.logger.debug(f"save message request received from {request.client.host}, client_key: {x_client_key}")
        client_key_to_desc = self.web_server_state.properties.client_info_properties
        # 发信方标识大小写不敏感
        x_client_key = server_utils.trim(x_client_key)

        if not x_client_key or x_client_key.lower() not in client_key_to_desc:
            self.logger.error(f"invalid client key:{x_client_key} from {request.client.host}!")
            raise HTTPException(status.HTTP_403_FORBIDDEN, "Invalid client key")

        self.logger.info(f"receive message from {client_key_to_desc[x_client_key]}")
        self.logger.debug(f"message content length: {len(send_data.content) if send_data.content else 0}")
        message_id = self.web_server_state.properties.data_manager.save_message(send_data.content, x_client_key)
        self.logger.debug(f"message saved with id: {message_id}")
        return {"status": "success", "message_id": message_id}

    # 读取指定发信方消息:client_key为空则获取所有未读消息
    async def get_client_unread(self, request: Request, size: int, token: str, client_key: Optional[str] = None):
        self.logger.debug(f"get unread messages request from {request.client.host}, size: {size}, client_key: {client_key}")

        if token != self.web_server_state.server_refresh_token:
            self.logger.error(f"unauthorized client {request.client.host} read messages")
            raise HTTPException(status.HTTP_401_UNAUTHORIZED)

        client_key_to_desc = self.web_server_state.properties.client_info_properties
        client_key = server_utils.trim(client_key)

        if client_key is not None:
            if client_key not in client_key_to_desc:
                self.logger.error(f"invalid client key:{client_key} from {request.client.host}!")
                raise HTTPException(status.HTTP_404_NOT_FOUND)
            self.logger.info(f"get unread messages from {client_key_to_desc[client_key]}")
        else:
            client_key = None
            self.logger.info("get unread messages from all sender")

        self.logger.debug(f"retrieving up to {size} unread messages with retry limit 5")
        msgs = self.web_server_state.properties.data_manager.get_recent_unread(size=size,minutes= 5, client_key=client_key)
        self.logger.info(f"read unread message: {len(msgs)}")
        return {"messages": msgs}

    def scheduler_running(self):
        return hasattr(self, 'web_server_state') and hasattr(self.web_server_state, 'properties') and hasattr(self.web_server_state.properties, 'scheduler') and self.web_server_state.properties.scheduler.running and not self.web_server_state.end_flag.is_set()

    # 监听外部停止事件
    async def _monitor_stop_event(self, stop_event: asyncio.Event):
        server_utils.logger_print("monitoring external stop event", self.logger, log_level=logging.DEBUG)
        await stop_event.wait()
        server_utils.logger_print("external stop event triggered", self.logger)

    # 定时刷新token
    def refresh_token(self):
        self.web_server_state.server_refresh_token = str(uuid4())
        server_utils.logger_print("server token refreshed!", self.logger)

    # 如果存在过期[3]天未读的数据则发出警告消息
    def expired_unread_data_warning(self):
        expired_days = 3
        expired_unread_data_size = self.web_server_state.properties.data_manager.get_older_unread_count(expired_days)
        if expired_unread_data_size > 0:
            server_utils.logger_print(f"there are {expired_unread_data_size} unread data that have expired for more than {expired_days} days, please handle it.", self.logger, log_level=logging.ERROR)

    # 本程序中在结束时需要做的事情
    async def cleanup_on_shutdown(self):
        self._end_do()

    # 该实例销毁时执行,只能被执行一次
    def _end_do(self):
        server_utils.run_once(self.__end_do_once)
    def __end_do_once(self):
        server_utils.logger_print("executing __end_do_once function", self.logger,log_level=logging.DEBUG)
        web_server_state_exist = hasattr(self, 'web_server_state') and self.web_server_state is not None
        server_config_exist = web_server_state_exist and hasattr(self.web_server_state, 'properties') and hasattr(self.web_server_state.properties,'server_config') and self.web_server_state.properties is not None and self.web_server_state.properties.server_config is not None
        RuntimeWebhookConfigManager.get_singleton_instance().cleanup_current_process()
        if server_config_exist:
            message_data_path = self.web_server_state.properties.server_config["message_data_path"]
            server_utils.logger_print(f"removing message_data_path from current process set: {message_data_path}", self.logger,log_level=logging.DEBUG)
            WebhookServer.cur_process_message_data_path.discard(message_data_path)

        if web_server_state_exist:
            server_utils.logger_print("resetting and clearing web_server_state", self.logger,log_level=logging.DEBUG)
            server_utils.run_once(self.web_server_state.reset_clear)
    def __del__(self):
        self._end_do()

    # 所有的定时任务都在这里添加
    def add_tasks(self,zone_info:ZoneInfo,config:dict,always_run:bool,end_time:time):
        task1=functools.partial(server_utils.run_safe_task,task_func=self.refresh_token,scheduler_check= self.scheduler_running, task_canceled_info="refresh_token_job cancelled(normal during shutdown)...",task_exception_info= "refresh_token_job occurred exception!",custom_logger= self.logger)
        task2=functools.partial(server_utils.run_safe_task,task_func=self.expired_unread_data_warning,scheduler_check= self.scheduler_running, task_canceled_info="expired_unread_data_warning_job cancelled(normal during shutdown)...",task_exception_info= "expired_unread_data_warning_job occurred exception!",custom_logger= self.logger)
        task3 = functools.partial(server_utils.run_safe_task, task_func=self.web_server_state.properties.data_manager.remove_excess_read_data, scheduler_check=self.scheduler_running, task_canceled_info="remove_excess_read_data_job cancelled(normal during shutdown)...", task_exception_info="remove_excess_read_data_job occurred exception!", custom_logger=self.logger,data_limit_num=config["data_limit_num"])
        task4=functools.partial(server_utils.run_safe_task,task_func=self.web_server_state.properties.data_manager.remove_expired_read_data,scheduler_check= self.scheduler_running, task_canceled_info="remove_expired_read_data_job cancelled(normal during shutdown)...",task_exception_info= "remove_expired_read_data_job occurred exception!",custom_logger= self.logger,expire_data_days=config["expire_data_days"])
        task7=functools.partial(server_utils.run_safe_task,task_func=self.cleanup_on_shutdown,scheduler_check= self.scheduler_running, task_canceled_info="cleanup_on_shutdown cancelled(normal during shutdown)...",task_exception_info= "cleanup_on_shutdown occurred exception!",custom_logger= self.logger)

        self.web_server_state.properties.scheduler.add_job(task1, 'interval', minutes=5, next_run_time=datetime.now(zone_info),id="refresh_token_job")
        self.web_server_state.properties.scheduler.add_job(task2, 'interval', hours=1, next_run_time=datetime.now(zone_info),id="expired_unread_data_warning_job")
        self.web_server_state.properties.scheduler.add_job(task3,'interval', minutes=1, id="remove_excess_read_data_job")
        self.web_server_state.properties.scheduler.add_job(task4,'interval', minutes=1, id="remove_expired_read_data_job")
        # 补充关闭时触发的定时任务
        if not always_run:
            self.web_server_state.properties.scheduler.add_job(task7, trigger='cron', hour=end_time.hour, minute=end_time.minute, id="cleanup_on_shutdown_job")



    # 启动uvicorn服务
    async def run_server(self, stop_event: Optional[multiprocessing.Event] = None):
        config = self.web_server_state.properties.server_config
        zone_info = self.web_server_state.properties.server_config_zone

        try:
            start_time, end_time = config["run_time"].split('-')
            start_time = datetime.strptime(start_time.strip(), "%H:%M").time()
            end_time = datetime.strptime(end_time.strip(), "%H:%M").time()
            now = datetime.now(zone_info).time()
            # 开始时间和结束时间一致,则一直运行
            always_run = (start_time == end_time)
            # 支持跨天运行
            can_run = (always_run or (start_time <= now <= end_time)
                       or (end_time < start_time and (now <= end_time or now >= start_time)))
            if can_run:
                if not self._check_config_unique_keys():
                    raise ValueError("config file has duplicate keys")

                self.add_tasks(zone_info,config,always_run,end_time)
                self.web_server_state.webhook_server = uvicorn.Server(uvicorn.Config(
                    self.app, host=config["host"], port=config["port"],
                    log_config=None, log_level=None, access_log=False
                ))

                # 如果提供了外部停止事件，创建监听任务
                if stop_event is not None:
                    server_utils.logger_print("external stop event provided, creating monitoring task", self.logger)
                    stop_monitor_task = asyncio.create_task(self._monitor_stop_event(stop_event))
                    server_task = asyncio.create_task(self.web_server_state.webhook_server.serve())

                    # 等待任一任务完成
                    done, pending = await asyncio.wait(
                        [server_task, stop_monitor_task],
                        return_when=asyncio.FIRST_COMPLETED
                    )

                    # 取消未完成的任务
                    for task in pending:
                        task.cancel()
                        try:
                            await task
                        except asyncio.CancelledError:
                            pass

                    # 检查是否是因为外部事件停止
                    # if stop_monitor_task in done:
                    #     server_utils.logger_print("server stopped by external event", self.logger)
                    #     # 设置服务器应该退出
                    #     if self.web_server_state.webhook_server:
                    #         self.web_server_state.webhook_server.should_exit = True
                else:
                    await self.web_server_state.webhook_server.serve()

        except asyncio.CancelledError:
            server_utils.logger_print("webhook server cancelled, shutting down...", self.logger,use_exception=True)
        finally:
            await self.cleanup_on_shutdown()

# ------------------ 服务管理 ------------------
# 根据配置文件启动web服务
async def run_server_with_path(config_path: str, stop_event: Optional[multiprocessing.Event] = None):
    server = WebhookServer()  # 每次调用创建新的实例
    server_utils.logger_print(f"loading server config from {config_path}", server.logger)
    try:
        server_utils.logger_print("setting up job defaults for scheduler", server.logger,log_level=logging.DEBUG)

        server_utils.logger_print(f"initializing server properties with config: {config_path}", server.logger,log_level=logging.DEBUG)
        server.web_server_state.properties = server_properties.ServerProperties(config_path, constants.DEFAULT_SCHEDULER_CONFIG)
        
        # 初始化logger
        if server.logger is None:
            server.logger = logging.getLogger(__name__)
        shutdown_exec.register(server.cleanup_on_shutdown)

        server_utils.logger_print("starting webhook server", server.logger)
        await server.run_server(stop_event)
    except KeyboardInterrupt:
        # 单独处理Ctrl+C
        server_utils.logger_print("server stopped by user request", server.logger)
        sys.exit(0)
    except BaseException as e:
        server_utils.logger_print(f"server failed because of {e}", server.logger, use_exception=True)
        sys.exit(1)
    finally:
        if server and hasattr(server, 'web_server_state') and hasattr(server.web_server_state, 'end_flag'):
            server.web_server_state.end_flag.set()

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--config", required=True, help="path to server config file")
    asyncio.run(run_server_with_path(parser.parse_args().config))
