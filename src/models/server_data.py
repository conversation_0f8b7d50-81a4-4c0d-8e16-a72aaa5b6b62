# 服务端数据传输相关的类
from datetime import datetime

from pydantic import BaseModel, Field


# 发信方发生数据到服务端
class SendData(BaseModel):
    content: str = Field(..., min_length=1, max_length=100)


# 服务端发送未读消息给第三方接收客户端
class MessageResponse(BaseModel):
    id: str
    message: str
    client_key: str
    reception_time: str

# 当前客户端界面显示
class MessageToGUI(BaseModel):
    message: str
    client_key: str
    reception_time: str
    is_read: int

class TokenException(Exception):
    """认证令牌异常基类"""

    def __init__(self, message: str, error_code: int = 4000):
        super().__init__(message)
        self.error_code = error_code  # 自定义错误码
        self.timestamp = datetime.now().isoformat()

    def __str__(self) -> str:
        return f"[{self.error_code}] {super().__str__()} @ {self.timestamp}"


class TokenExpiredError(TokenException):
    """令牌已过期（严重错误）"""

    def __init__(self, expired_at: datetime = None):
        if expired_at is None:
            expired_at = datetime.now()
        msg = f"Token expired at {expired_at.strftime('%Y-%m-%d %H:%M:%S')}"
        super().__init__(msg, error_code=4101)
        self.expired_at = expired_at  # 令牌过期时间


class TokenNearExpirationWarning(TokenException):
    """令牌即将过期（警告）"""

    def __init__(self, remain_seconds: int):
        msg = f"Token will expire in {remain_seconds} seconds"
        super().__init__(msg, error_code=2101)
        self.remain_seconds = remain_seconds
