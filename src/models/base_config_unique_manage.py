# 多进程配置项值唯一性管理,由于可能涉及到的场景不同,这里就只是一个基础的抽象层
import logging
import os
import sqlite3
import threading
from typing import Optional, Any, Dict, Type
from zoneinfo import ZoneInfo

from config import constants
from models.sqlite_base_manager import SQLiteBaseManager
from utils import shutdown_exec_funct, server_utils

logger = logging.getLogger(__name__)
class BaseConfigUniquenessManager(SQLiteBaseManager):
    """多进程配置项唯一性管理器基类:不同场景下对多进程配置项值唯一的要求处理逻辑不同所涉及到的表结构也可能不同,因此抽象出基类"""
    # 存储各子类的单例
    _instances: Dict[Type["BaseConfigUniquenessManager"], "BaseConfigUniquenessManager"] = {}
    # 锁，保证多线程下单例创建的安全性
    _instances_lock = threading.Lock()
    # 正在创建中的类标记集合
    _creating: set[Type["BaseConfigUniquenessManager"]] = set()

    def __new__(cls, *args, **kwargs):
        # 除非正在通过 get_singleton_instance 创建，否则禁止直接构造
        if cls not in BaseConfigUniquenessManager._creating:
            raise RuntimeError(
                f"{cls.__name__} cannot be instantiated directly; "
                "use get_singleton_instance()"
            )
        return super().__new__(cls)
    def __init__(self, db_path: str = constants.CROSS_PROCESS_DATA_BASE_PATH, enable_sql_logging: bool = True,heartbeat_interval:int=30,heartbeat_timeout:int=300, zone: ZoneInfo = None):
        super().__init__(db_path=db_path,enable_sql_logging= enable_sql_logging,zone=zone)
        self._pid = os.getpid()  # 当前进程ID
        self._heartbeat_interval = heartbeat_interval
        self._heartbeat_timeout = heartbeat_timeout

        self._heartbeat_thread: Optional[threading.Thread] = None
        self._stop_heartbeat_event = threading.Event()
        self._heartbeat_failure_count = 0  # 心跳失败计数器
        shutdown_exec_funct.register(self.cleanup_current_process)

    @classmethod
    def get_singleton_instance(cls, *args, **kwargs) -> "BaseConfigUniquenessManager":# noqa
        """
        返回子类的单例：首次调用时创建实例并保存，后续调用忽略参数。只能从该方法获取实例
        线程安全：使用类级锁保证在并发环境下只创建一个实例,且禁止从外部直接 new/cls()。
        """
        with BaseConfigUniquenessManager._instances_lock:
            if cls not in BaseConfigUniquenessManager._instances:
                # 标记正在创建，放行 __new__
                BaseConfigUniquenessManager._creating.add(cls)
                try:
                    BaseConfigUniquenessManager._instances[cls] = cls(*args, **kwargs)
                finally:
                    BaseConfigUniquenessManager._creating.remove(cls)
            return BaseConfigUniquenessManager._instances[cls]
    def _cleanup_current_process(self):
        """当前进程结束时清理相关资源"""
        raise NotImplementedError("subclasses must implement _cleanup_current_process")
    def _heartbeat_worker(self):
        """心跳工作线程具体执行逻辑"""
        raise NotImplementedError("subclasses must implement _heartbeat_worker")

    def _cleanup_inactive_records(self, cur: sqlite3.Cursor):
        """清理过期的配置项记录:在注册配置项前和心跳机制中使用"""
        raise NotImplementedError("subclasses must implement _cleanup_inactive_records")
    def register_config(self, *args, **kwargs) -> bool:
        """
        注册配置项键值对
        返回True表示注册成功，False表示注册失败（键值对已存在或其他原因）
        """
        raise NotImplementedError("subclasses must implement register_config")

    def unregister_config(self, *args, **kwargs) -> bool:
        """
        注销配置项键值对: 注销当前进程的配置项键值对
        True表示成功将已经注册的配置项注销,False表示该配置项不存在或已经被注销失败
        """
        raise NotImplementedError("subclasses must implement unregister_config")
    def update_config(self, *args, **kwargs) -> bool:
        """
        更新配置项键值对,有点子类不需要实现该方法
        返回True表示更新成功，False表示键值对不存在
        """
        raise NotImplementedError("subclasses must implement update_config")
    def get_valid_values_for_key(self, config_key: str) ->Any:
        """
        获取数据库中所有有效的指定配置项的值
        """
        raise NotImplementedError("subclasses must implement get_active_values_for_key")
    def check_config_registrable(self,*args, **kwargs):
        """
        检查配置项键值对是否可用（未被其他进程使用）
        注意：此检查不是原子操作，仅适用于启动前的预检查
        如果只进行检测判断,不进行注册,则可以使用此方法
        """
        raise NotImplementedError("subclasses must implement check_config_registrable")

    def cleanup_current_process(self):
        server_utils.run_once(self._cleanup_current_process)

    def _start_heartbeat_thread(self):
        """启动心跳线程（仅当有注册项时运行）"""
        if self._heartbeat_thread and self._heartbeat_thread.is_alive():
            return
        self._stop_heartbeat_event.clear()
        cur_class_name = self.__class__.__name__
        self._heartbeat_thread = threading.Thread(target=self._heartbeat_worker,daemon=True,name=f"{cur_class_name}_ConfigHeartbeat")
        self._heartbeat_thread.start()
        server_utils.logger_print(msg=f"{cur_class_name} heartbeat thread started", custom_logger=logger, log_level=logging.DEBUG)

    def _stop_heartbeat_thread(self):
        """停止心跳线程"""
        cur_class_name = self.__class__.__name__
        if hasattr(self, "_stop_event") and self._stop_event is not None:
            self._stop_event.set()
        if hasattr(self, "_stop_heartbeat_event") and self._stop_heartbeat_event is not None:
            self._stop_heartbeat_event.set()
        if self._heartbeat_thread:
            try:
                self._heartbeat_thread.join(timeout=5.0)
            except Exception: # noqa
                server_utils.logger_print(msg="error joining heartbeat thread!",custom_logger=logger,log_level=logging.ERROR,use_exception=True)
            finally:
                self._heartbeat_thread = None
                server_utils.logger_print(msg=f"{cur_class_name} heartbeat thread stopped", custom_logger=logger, log_level=logging.DEBUG)

    def _restart_heartbeat_thread(self):
        """重启心跳线程"""
        self._stop_heartbeat_thread()
        self._start_heartbeat_thread()
