# 多进程gui界面配置项唯一性锁
import configparser
import logging
import sqlite3
import threading
import time
from typing import Dict, Optional, Tuple, List
from zoneinfo import ZoneInfo

from config import constants, gui_config_check, gui_constants
from models.base_config_unique_manage import BaseConfigUniquenessManager
from models.sqlite_base_manager import SQLiteBaseManager
from utils import server_utils

logger = logging.getLogger(__name__)

class MultiProcessUIConfigManager(BaseConfigUniquenessManager):
    """多进程界面配置文件唯一性管理器（单例模式）
    注意事项:
       1. 由于是给前端界面显示的,本类中的异常捕获处理是抛出给前端看的
       2. 本数据库表中时间字段如果需要给前端显示,则需要进行时区转换,本类中 CURRENT_TIMESTAMP 存储的是UTC时区,需要转换

    主要功能：
    1. 管理配置文件的占用状态，确保同一配置文件不被多个进程同时使用
    2. 维护配置文件记录的有效性检测
    3. 提供配置文件注册、注销、更新功能
    4. 心跳机制保证进程存活检测和资源清理
    """

    HEARTBEAT_TIMEOUT = 300  # 5分钟心跳超时 [s]
    HEARTBEAT_INTERVAL = 15  # 15秒心跳间隔 [s]

    def __init__(self, db_path: str = constants.CROSS_PROCESS_DATA_BASE_PATH, enable_sql_logging: bool = True, zone: ZoneInfo = None):
        server_utils.logger_print(msg=f"initializing multiprocess UI config manager with db_path: {db_path}, sql_logging: {enable_sql_logging}", custom_logger=logger, log_level=logging.INFO)
        super().__init__(
            db_path=db_path,
            enable_sql_logging=enable_sql_logging,
            heartbeat_interval=MultiProcessUIConfigManager.HEARTBEAT_INTERVAL,
            heartbeat_timeout=MultiProcessUIConfigManager.HEARTBEAT_TIMEOUT,
            zone=zone
        )
        server_utils.logger_print(msg="multiprocess UI config manager initialized", custom_logger=logger, log_level=logging.INFO)
        self._config_lock = threading.RLock()  # 配置操作的并发锁
        self._registered_config_path: Optional[str] = None  # 当前进程注册的配置文件路径
        self._registered_config_id: Optional[int] = None    # 当前进程注册的配置记录ID
        self._is_registered = False  # 标记当前进程是否已注册配置

    def _init_db(self):
        """初始化数据库表结构"""
        def init_tables(cur: sqlite3.Cursor):
            # 创建配置文件使用记录表
            init_sql = """
            CREATE TABLE IF NOT EXISTS config_file_record (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,                   -- 配置加载界面显示的名称
                file_path TEXT NOT NULL UNIQUE,       -- 文件绝对路径
                process_id INTEGER,                   -- 占用进程PID
                last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 最后使用时间
                checksum TEXT NOT NULL,               -- SHA-256文件哈希
                is_active INTEGER NOT NULL DEFAULT 1 CHECK(is_active IN (0, 1)), -- 当前配置是否有效
                fail_reason TEXT,                     -- 当前配置失效原因
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP   -- 更新时间
            );
            
            CREATE TABLE IF NOT EXISTS config_unique_constraints (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                config_file_record_id INTEGER NOT NULL,  -- 关联 config_file_record.id
                config_key TEXT NOT NULL,            -- 配置键
                config_value TEXT NOT NULL,          -- 配置值
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP   -- 更新时间
            );
            
            -- 优化索引
            CREATE INDEX IF NOT EXISTS idx_file_path ON config_file_record (file_path);
            CREATE INDEX IF NOT EXISTS idx_process_id ON config_file_record (process_id);
            CREATE INDEX IF NOT EXISTS idx_last_used ON config_file_record (last_used);
            CREATE INDEX IF NOT EXISTS idx_is_active ON config_file_record (is_active);
            
            CREATE INDEX IF NOT EXISTS idx_config_file_record_id ON config_unique_constraints (config_file_record_id);
            CREATE INDEX IF NOT EXISTS idx_config_key_value ON config_unique_constraints (config_key, config_value);
            """
            cur.executescript(init_sql)

        self._exec_retry(init_tables)
        server_utils.logger_print(msg="multiprocess UI config database tables initialized", custom_logger=logger, log_level=logging.INFO)

    def _start_heartbeat_thread(self):
        """启动心跳线程（仅当已注册配置时运行）"""
        with self._config_lock:
            if not self._is_registered:
                return
            super()._start_heartbeat_thread()
    def _update_heartbeat(self):
        """更新当前进程配置的心跳时间"""
        with self._config_lock:
            if not self._is_registered or not self._registered_config_path:
                return
        def _update(cur: sqlite3.Cursor):
            cur.execute("""
            UPDATE config_file_record SET last_used = CURRENT_TIMESTAMP WHERE id = ? ;
            """, (self._registered_config_id,))

        self._exec_retry(_update)
        server_utils.logger_print(msg=f"updated heartbeat for config: {self._registered_config_path}", custom_logger=logger, log_level=logging.DEBUG)
    def _heartbeat_worker(self):
        """心跳工作线程"""
        while not self._stop_heartbeat_event.is_set():
            try:
                with self._config_lock:
                    if not self._is_registered:
                        server_utils.logger_print(msg="no registered config, stopping heartbeat", custom_logger=logger, log_level=logging.INFO)
                        break

                self._update_heartbeat()
                self._release_validate_cleanup_invalid_records()

                with self._config_lock:
                    self._heartbeat_failure_count = 0  # 重置失败计数器
                server_utils.logger_print(msg=f"heartbeat thread running, interval: {self._heartbeat_interval}s",custom_logger=logger, log_level=logging.DEBUG)
                start_time = time.time()
                if self._stop_heartbeat_event.wait(timeout=self._heartbeat_interval):
                    break
                server_utils.logger_print(msg=f"gui heartbeat worker running, elapsed time: {time.time() - start_time}", custom_logger=logger, log_level=logging.DEBUG)
            except Exception as heartbeat_ex:
                with self._config_lock:
                    self._heartbeat_failure_count += 1
                    cur_failure_count = self._heartbeat_failure_count
                server_utils.logger_print(msg=f"heartbeat thread error: {heartbeat_ex}", custom_logger=logger, log_level=logging.ERROR)

                # 连续失败多次后重启线程
                if cur_failure_count >= 3:
                    server_utils.logger_print(msg="heartbeat thread failed 3 times, attempting to restart thread", custom_logger=logger, log_level=logging.ERROR)
                    self._restart_heartbeat_thread()
                    return
                if self._stop_heartbeat_event.wait(timeout=5):# 出错后等待5秒再重试
                    break

        server_utils.logger_print(msg="gui heartbeat worker stopped", custom_logger=logger, log_level=logging.INFO)
    @staticmethod
    def _release_fake_process_config(cur: sqlite3.Cursor):
        """
        释放被假进程占用的配置
        过滤机制:
        1. 对应进程检测已经死亡
        和"心跳超时"无关,进程死亡就是进程死亡,就释放对应占用的配置文件
        """
        # 获取所有数据库中占用配置的PID
        cur.execute("SELECT process_id FROM config_file_record WHERE process_id IS NOT NULL;")
        db_pids = {row[0] for row in cur.fetchall() if row[0] is not None}
        if db_pids:
            # 获取系统所有活动PID
            active_pids = server_utils.get_active_pids()
            # 找出已终止的PID
            dead_pids = db_pids - active_pids
            # 释放已死进程占用的配置
            if dead_pids:
                placeholders = ",".join('?' * len(dead_pids))
                cur.execute(f"""
                    UPDATE config_file_record SET process_id = NULL WHERE process_id IN ({placeholders});
                    """, tuple(dead_pids))
                logger.debug(f"released unused config for dead processes: {dead_pids}")
    def _cleanup_inactive_records(self, cur: sqlite3.Cursor):
        """清理失效的数据记录 --- 补充操作,一般在设置数据失效的情况下同步会删除失效的唯一约束记录"""
        # config_file_record 表数据不删除,后台不删除该数据,而是用户手动调用删除操作
        cur.execute("""
            DELETE FROM config_unique_constraints WHERE config_file_record_id IN 
            (SELECT id FROM config_file_record WHERE is_active = 0);
        """)
        cur_deleted_count = cur.rowcount
        cur.execute("""
            DELETE FROM config_unique_constraints WHERE config_file_record_id NOT IN 
            (SELECT id FROM config_file_record);
        """)
        cur_deleted_count += cur.rowcount
        if cur_deleted_count>0:
            logger.info(f"released invalid config records - unique_constraints!")

    def ___release_validate_cleanup_invalid_records(self,cur: sqlite3.Cursor):
        # 释放假进程占用的配置
        MultiProcessUIConfigManager._release_fake_process_config(cur)
        # 检测未被占用配置的有效性
        self._validate_unoccupied_valid_configs(cur)
        self._cleanup_inactive_records(cur)
    def _release_validate_cleanup_invalid_records(self):
        """释放假进程占用的配置并检测未被占用配置的有效性并清除失效的记录 --- 一般在数据存在变化前使用"""
        self._exec_retry(self.___release_validate_cleanup_invalid_records)

    def _validate_unoccupied_valid_configs(self, cur: sqlite3.Cursor):
        """检测未被占用且有效的配置此刻的有效性"""
        # 获取所有未被占用且标记为有效的配置
        cur.execute("""
        SELECT id, file_path, checksum FROM config_file_record WHERE process_id IS NULL AND is_active = 1;
        """)

        configs_to_validate = cur.fetchall()

        for config_file_record_id, file_path, stored_checksum in configs_to_validate:
            try:
                # 检查文件有效性
                gui_config_check.check_loading_gui_list_config_file(file_path,stored_checksum)
            except Exception as error_reason:
                # 当前配置文件失效,标记为无效
                self._update_invalid_config_state(file_path, config_file_record_id, str(error_reason), cur)

    def _update_invalid_config_state(self, file_path:str, config_file_record_id: int, error_reason: str, cur: sqlite3.Cursor):
        """标记配置为无效并清理相关配置项唯一记录"""
        with self._config_lock:
            cur.execute("""
                    UPDATE config_file_record 
                    SET is_active = 0, fail_reason = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ? ;
                    """, (error_reason, config_file_record_id))

            # 清理对应的配置项约束记录
            cur.execute("""
                    DELETE FROM config_unique_constraints WHERE config_file_record_id = ? ;
                    """, (config_file_record_id,))

            logger.warning(f"marked config {file_path} as invalid: {error_reason}")

    def _get_all_config_records_to_by_status(self,cur: sqlite3.Cursor)->Tuple[List[Dict],List[Dict],List[Dict]]:
        """获取此刻所有配置记录并区分出无效和有效/有效未被占用的记录:刷新列表返回的配置记录数据"""
        invalid_records=[]
        valid_records=[]
        used_by_process_records=[]
        cur.execute("""
            SELECT id, name, file_path, process_id, last_used,is_active, fail_reason FROM config_file_record;
            """)
        result = cur.fetchall()
        if result:
            columns = [desc[0] for desc in cur.description]
            for row in result:
                record = dict(zip(columns, row))
                # last_used 时间戳时区转换转换当前时区
                record['last_used'] = SQLiteBaseManager.convert_utc_str(record['last_used'], self.zone)
                if record['is_active']==0:
                    invalid_records.append(record)
                elif record['process_id'] is None:
                    valid_records.append(record)
                else:
                    used_by_process_records.append(record)
        return invalid_records, valid_records, used_by_process_records
    @staticmethod
    def _extract_unique_constraints_from_config_file(file_path: str) -> dict[str, str]:
        """
        从配置文件中提取需要唯一性约束的配置项
        Returns:
            List of (config_key, config_value) tuples
        """
        server_config = configparser.ConfigParser(interpolation=None)
        server_config.read(file_path, encoding="utf-8")
        config_items = {}
        for key in gui_constants.GUI_SERVER_CONFIG_UNIQUE_KEYS:
            # 所以的唯一性配置项都在server节点中
            section='server'
            if server_config.has_option(section, key):
                value = server_config.get(section, key)
                config_items[key]= value
            elif key not in gui_constants.USER_CUSTOM_KEYS:
                logger.warning(f"config item {key} not found in {file_path}")
                raise ValueError(f"配置文件必要配置项缺失，其中[{key}]是缺失的")
            else:
                config_items[key] = ''

        return config_items
    def load_import_config(self, file_path: str) -> bool:
        """仅仅加载配置文件,但不使用对应的配置文件
        Args:
            file_path: 配置文件绝对路径

        需要在GUI_SERVER_CONFIG_UNIQUE_KEYS-USER_CUSTOM_KEYS的范围内校验多进程唯一性
        Returns:
            bool: 加载成功返回True，失败返回False
        """
        logger.info(f"attempting to load import config---{file_path}")

        if not file_path or not isinstance(file_path, str):
            logger.error("file_path must be a non-empty string")
            raise ValueError("需要加载的配置文件路径不能为空")

        # 转换为绝对路径
        abs_file_path = server_utils.get_real_path_create_dir(file_path)

        def __load_import_with_cleanup(cur: sqlite3.Cursor):
            server_utils.logger_print(msg="executing cleanup and validation before loading config", custom_logger=logger, log_level=logging.DEBUG)
            # 在事务内进行清理操作
            self.___release_validate_cleanup_invalid_records(cur)
            # 配置文件有效性校验
            gui_config_check.check_new_get_config_file(abs_file_path)

            server_utils.logger_print(msg="calculating file hash and extracting config items", custom_logger=logger, log_level=logging.DEBUG)
            # 计算文件哈希
            checksum = server_utils.file_hash(abs_file_path)
            # 提取唯一性的配置项
            config_items = MultiProcessUIConfigManager._extract_unique_constraints_from_config_file(abs_file_path)
            config_name=config_items['app_name']
            # 插入配置记录
            cur.execute("""
            INSERT INTO config_file_record (name, file_path, checksum, last_used)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP);
            """, (config_name, abs_file_path, checksum))
            config_record_id = cur.lastrowid
            # 插入配置项约束记录
            for config_key, config_value in config_items.items():
                cur.execute("""
                INSERT INTO config_unique_constraints (config_file_record_id, config_key, config_value)
                VALUES (?, ?, ?);
                """, (config_record_id, config_key, config_value))

            server_utils.logger_print(msg=f"successfully loaded import config: {config_name} at {abs_file_path}", custom_logger=logger, log_level=logging.INFO)
            return True

        try:
            return self._exec_retry(__load_import_with_cleanup)
        except Exception as failed_register_ex: # noqa
            server_utils.logger_print(msg=f"error loading config: {abs_file_path}", custom_logger=logger, log_level=logging.ERROR, use_exception=True)
            # 给前端提示
            raise ValueError(f"不能使用{abs_file_path}配置文件,原因为:{str(failed_register_ex)}")
    def register_config(self, file_path: str) -> bool:
        """注册并使用配置文件
        Args:
            file_path: 配置文件绝对路径

        需要在GUI_SERVER_CONFIG_UNIQUE_KEYS-USER_CUSTOM_KEYS的范围内校验多进程唯一性
        Returns:
            bool: 注册成功返回True，失败返回False
        """
        logger.info(f"attempting to register config---{file_path}")

        if not file_path or not isinstance(file_path, str):
            logger.error("file_path must be a non-empty string")
            raise ValueError("注册的配置文件路径不能为空")

        # 检查当前进程是否已注册配置
        with self._config_lock:
            if self._is_registered:
                logger.error(f"process {self._pid} already registered a config")
                return False

        # 转换为绝对路径
        abs_file_path = server_utils.get_real_path_create_dir(file_path)

        def _register_with_cleanup(cur: sqlite3.Cursor):
            server_utils.logger_print(msg="executing cleanup and validation before registering config", custom_logger=logger, log_level=logging.DEBUG)
            # 在事务内进行清理操作
            self.___release_validate_cleanup_invalid_records(cur)
            # 配置文件有效性校验
            gui_config_check.check_new_get_config_file(abs_file_path)
            server_utils.logger_print(msg="calculating file hash and extracting config items", custom_logger=logger, log_level=logging.DEBUG)
            # 计算文件哈希
            checksum = server_utils.file_hash(abs_file_path)
            # 提取唯一性的配置项
            config_items = MultiProcessUIConfigManager._extract_unique_constraints_from_config_file(abs_file_path)
            config_name=config_items['app_name']
            server_utils.logger_print(msg=f"inserting config record for: {config_name}", custom_logger=logger, log_level=logging.DEBUG)
            # 插入配置记录
            cur.execute("""
            INSERT INTO config_file_record (name, file_path, process_id, checksum, last_used)
            VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP);
            """, (config_name, abs_file_path, self._pid, checksum))
            config_record_id = cur.lastrowid
            server_utils.logger_print(msg="inserting config unique constraints", custom_logger=logger, log_level=logging.DEBUG)
            # 插入配置项约束记录
            for config_key, config_value in config_items.items():
                cur.execute("""
                INSERT INTO config_unique_constraints (config_file_record_id, config_key, config_value)
                VALUES (?, ?, ?);
                """, (config_record_id, config_key, config_value))

            server_utils.logger_print(msg="updating local state", custom_logger=logger, log_level=logging.DEBUG)
            # 更新本地状态
            with self._config_lock:
                self._registered_config_path = abs_file_path
                self._registered_config_id = config_record_id
                self._is_registered = True

            # 启动心跳线程
            self._start_heartbeat_thread()

            server_utils.logger_print(msg=f"successfully registered config: {config_name} at {abs_file_path}", custom_logger=logger, log_level=logging.INFO)
            return True

        try:
            return self._exec_retry(_register_with_cleanup)
        except Exception as failed_register_ex: # noqa
            server_utils.logger_print(msg=f"error registering config: {abs_file_path}", custom_logger=logger, log_level=logging.ERROR, use_exception=True)
            # 给前端提示
            raise ValueError(f"不能使用{abs_file_path}配置文件,原因为:{str(failed_register_ex)}")

    def update_config(self, section:str, config_key_values:dict[str,str]) -> bool:
        """更新配置文件项,如果其是唯一配置项,则对应数据库项也会修改
        Args:
            section: 配置项所属节点
            config_key_values: 配置项键-值对 至少一个配置项
        Returns:
            bool: 更新成功返回True，失败返回False
        """
        if not config_key_values or not isinstance(config_key_values, dict):
            logger.error("config_key_values must be a non-empty dict")
            raise ValueError("新增或者修改的配置项不能为空")
        with self._config_lock:
            if not self._is_registered:
                logger.warning(f"server config not registered in this process,cannot update")
                raise ValueError("当前进程未注册配置文件,请先注册配置文件后再更新")

        def _update_with_cleanup(cur: sqlite3.Cursor):
            server_utils.logger_print(msg="executing cleanup and validation before updating config", custom_logger=logger, log_level=logging.DEBUG)
            # 在事务内进行清理操作
            self.___release_validate_cleanup_invalid_records(cur)
            server_utils.logger_print(msg="checking config file integrity", custom_logger=logger, log_level=logging.DEBUG)
            # 比较哈希值新值和旧值是否相同:避免被外部程序修改
            cur.execute("SELECT checksum FROM config_file_record WHERE id = ? ;", (self._registered_config_id,))
            stored_checksum = cur.fetchone()[0]
            # 保证配置文件没有被非程序改动
            gui_config_check.check_loading_gui_list_config_file(self._registered_config_path,stored_checksum)
            # 如果该配置项是唯一项,需要校验其唯一性
            for key, value in config_key_values.items():
                # value是不能为空的,校验输入的配置项key-value是否符合格式要求
                gui_config_check.check_config_section_key_value_format_valid(section, key, value)
                if section =='server' and key in gui_constants.GUI_SERVER_CONFIG_UNIQUE_KEYS:
                    cur.execute("""
                        SELECT COUNT(*) FROM config_unique_constraints cuc
                        JOIN config_file_record cfr ON cuc.config_file_record_id = cfr.id
                        WHERE cuc.config_key = ? AND cuc.config_value = ? and cuc.config_file_record_id != ? ;
                        """, (key, value,self._registered_config_id))

                    if cur.fetchone()[0] != 0:
                        server_utils.logger_print(msg=f"config item conflict before update: {key}={value}", custom_logger=logger, log_level=logging.ERROR)
                        raise ValueError(f"当前{key}配置项值{value}已被其他配置使用,请更换新的值后再尝试更新")
                    # 更新数据库
                    cur.execute("""
                    UPDATE config_unique_constraints SET config_value = ? WHERE config_file_record_id = ? AND config_key = ? ;
                    """, (value, self._registered_config_id, key))
            # 更新实际的文件内容
            server_utils.update_config(self._registered_config_path, section, config_key_values)
            # 更新配置记录
            new_checksum_calc=server_utils.file_hash(self._registered_config_path)
            cur.execute("""
            UPDATE config_file_record SET checksum = ?, updated_at = CURRENT_TIMESTAMP, last_used = CURRENT_TIMESTAMP WHERE id = ? ;
            """, (new_checksum_calc, self._registered_config_id))
            server_utils.logger_print(msg=f"successfully updated config: {self._registered_config_path}", custom_logger=logger, log_level=logging.INFO)
            return True

        try:
            return self._exec_retry(_update_with_cleanup)
        except Exception as error_update_ex:
            server_utils.logger_print(msg="error updating config", custom_logger=logger, log_level=logging.ERROR, use_exception=True)
            raise ValueError(f"更新配置文件{self._registered_config_path}时发生错误\n错误原因:{str(error_update_ex)}")

    def unregister_config(self) -> bool:
        """注销当前进程的配置:只能注销自己的配置,不能注销其他进程的配置
        配置文件不被当前进程占用,配置文件记录本身不删除
        Returns:
            bool: 注销成功返回True，失败返回False
        """
        server_utils.logger_print(msg=f"{self.__class__.__name__} attempting to unregister config",custom_logger=logger,log_level=logging.INFO)
        with self._config_lock:
            if not self._is_registered:
                server_utils.logger_print(msg="this process has not registered a config, cannot unregister!",custom_logger=logger,log_level=logging.WARNING)
                return False
        def _unregister(cur: sqlite3.Cursor):
            server_utils.logger_print(msg=f"{self.__class__.__name__} attempting to unregister config: {self._registered_config_path} when run sql",custom_logger=logger,log_level=logging.INFO)
            # 释放配置占用
            cur.execute("""
            UPDATE config_file_record SET process_id = NULL, last_used = CURRENT_TIMESTAMP,updated_at = CURRENT_TIMESTAMP WHERE id = ? ;
            """, (self._registered_config_id,))

            if cur.rowcount != 1:
                server_utils.logger_print(msg=f"failed to unregister config: {self._registered_config_path}",custom_logger=logger,log_level=logging.ERROR)
                return False

            # 更新本地状态
            with self._config_lock:
                self._registered_config_path = None
                self._registered_config_id = None
                self._is_registered = False

            # 停止心跳线程
            self._stop_heartbeat_thread()

            server_utils.logger_print(msg=f"{self.__class__.__name__} successfully unregistered config: {self._registered_config_path}",custom_logger=logger,log_level=logging.INFO)
            return True

        try:
            return self._exec_retry(_unregister)
        except Exception:# noqa
            logger.exception(f"error unregistering config!")
            return False
    def main_gui_load_config(self,server_config_path:str):
        """加载配置文件到主GUI进程"""
        if not server_config_path or not isinstance(server_config_path, str):
            logger.error("file_path must be a non-empty string")
            raise ValueError("注册的配置文件路径不能为空")

        # 转换为绝对路径
        abs_file_path = server_utils.get_real_path_create_dir(server_config_path)
        with self._config_lock:
            if  self._is_registered:
                if self._registered_config_path!= abs_file_path:
                    logger.warning("server config has already registered in this process,please select other config file to load!")
                    raise ValueError("当前进程已经注册配置文件,不能再次加载新的配置文件")
                else:
                    logger.info("server config has already registered in this process,skip loading new config")
                    return
        def _load_db_config(cur: sqlite3.Cursor,record:dict[str,str]):
            # 判断该配置文件是否在数据库中
            process_id=record['process_id']
            is_active=int(record['is_active'])
            fail_reason=record['fail_reason']
            checksum=record['checksum']
            server_config_id=int(record['id'])
            if process_id:
                # 该配置文件已被占用,前面已经判断配置文件被当前进程占用的情况,所以说如果存在进程号不为空的记录,说明该配置文件已被其他进程占用
                server_utils.logger_print(msg=f"config file {abs_file_path} has already registered in other process: {process_id}", custom_logger=logger, log_level=logging.ERROR)
                raise ValueError(f"配置文件{abs_file_path}已被其他进程:[{process_id}]占用!")
            elif is_active == 0:
                # 该配置文件已失效,前面已经判断配置文件是否有效,所以说如果is_active为0,说明该配置文件已失效
                server_utils.logger_print(msg=f"config file {abs_file_path} has already been marked as invalid", custom_logger=logger, log_level=logging.ERROR)
                raise ValueError(f"配置文件{abs_file_path}已失效,失效原因是:{fail_reason}!")
            else:
                server_utils.logger_print(msg="validating config file integrity", custom_logger=logger, log_level=logging.DEBUG)
                # 该配置文件有效且存在数据库中,有效性校验之后更新数据库
                gui_config_check.check_loading_gui_list_config_file(abs_file_path,checksum)
                server_utils.logger_print(msg="updating config record with current process", custom_logger=logger, log_level=logging.DEBUG)
                cur.execute("""
                UPDATE config_file_record SET process_id = ?, updated_at = CURRENT_TIMESTAMP, last_used = CURRENT_TIMESTAMP WHERE id = ? ;
                """, (self._pid, server_config_id))
                # 更新本地状态
                with self._config_lock:
                    self._registered_config_path = abs_file_path
                    self._registered_config_id = server_config_id
                    self._is_registered = True

                # 启动心跳线程
                self._start_heartbeat_thread()
                server_utils.logger_print(msg=f"successfully loaded config: {abs_file_path}", custom_logger=logger, log_level=logging.INFO)

        try:
            self._release_validate_cleanup_invalid_records()
            record_in_db=self.get_config_path_in_db(abs_file_path)
            if not record_in_db:
                server_utils.logger_print(msg="config not found, registering new config", custom_logger=logger, log_level=logging.INFO)
                self.register_config(abs_file_path)
            else:
                self._exec_retry(lambda cur: _load_db_config(cur,record_in_db))
        except Exception:
            server_utils.logger_print(msg=f"error loading config: {abs_file_path}", custom_logger=logger, log_level=logging.ERROR, use_exception=True)
            raise

    def get_valid_values_for_key(self, config_key: str) -> set[str]:
        """获取指定配置键的所有有效值"""
        def _query(cur: sqlite3.Cursor):
            cur.execute("""
            SELECT cuc.config_value
            FROM config_unique_constraints cuc JOIN config_file_record cfr ON cuc.config_file_record_id = cfr.id
            WHERE cfr.is_active = 1 AND cuc.config_key = ? ;
            """, (config_key,))
            return SQLiteBaseManager.get_field_datas(rows=cur.fetchall(), field_index=0, need_only=True)

        try:
            return self._exec_retry(_query)
        except Exception: # noqa
            logger.exception(f"error getting valid values for key {config_key}!")
            raise
    def get_config_path_in_db(self, server_config_path:str) -> dict[str,str]|None:
        """检查数据库中是否存在指定路径的配置文件记录"""
        def query_data(cur: sqlite3.Cursor):
            cur.execute("""SELECT process_id,is_active,fail_reason,checksum,id  FROM config_file_record WHERE file_path = ? ;""", (server_config_path,))
            record = cur.fetchone()
            return dict(zip([desc[0] for desc in cur.description], record)) if record else None
        return self._exec_retry(query_data)
    def check_config_path_in_db(self, server_config_path:str) -> bool:
        """检查数据库中是否存在指定路径的配置文件记录"""
        query = """SELECT COUNT(*)  FROM config_file_record WHERE file_path = ? ;"""
        return self._exec_retry(lambda cur: cur.execute(query, (server_config_path,)).fetchone()[0])>0
    def check_config_in_db(self) -> bool:
        """检查数据库中是否存在配置文件记录:配置加载界面中首次加载判断"""
        query = """SELECT COUNT(*)  FROM config_file_record"""
        return self._exec_retry(lambda cur: cur.execute(query).fetchone()[0])>0
    def check_value_exist_in_db(self,config_key:str,config_value:str)->bool:
        """检查数据库中是否存在指定配置项:一般使用在注册前或手动加载配置文件前"""
        query = """SELECT COUNT(*)  FROM config_unique_constraints WHERE config_key = ? AND config_value = ? ;"""
        return self._exec_retry(lambda cur: cur.execute(query, (config_key,config_value)).fetchone()[0])>0
    def get_all_server_config_paths_in_db(self) -> set[str]:
        """获取数据库中所有服务器配置路径[无论当前记录中其配置是否有效]"""
        def _query(cur: sqlite3.Cursor):
            cur.execute("""SELECT file_path FROM config_file_record;""")
            return SQLiteBaseManager.get_field_datas(rows=cur.fetchall(), field_index=0, need_only=True)
        try:
            return self._exec_retry(_query)
        except Exception: # noqa
            logger.exception(f"error getting all server config paths!")
            raise

    def check_config_registrable(self, file_path: str) -> Tuple[bool, Optional[str]]:
        """检查配置文件是否可以注册

        Args:
            file_path: 配置文件路径

        Returns:
            (can_register, error_reason): 是否可以注册和错误原因
        """
        if not file_path or not isinstance(file_path, str):
            return False, "配置文件路径不能为空"
        abs_file_path = server_utils.get_real_path_create_dir(file_path)
        try:
            self._release_validate_cleanup_invalid_records()
            # 配置文件有效性校验
            gui_config_check.check_new_get_config_file(abs_file_path)
            return True, None
        except Exception as register_ex:
            server_utils.logger_print(msg="error checking config registrable", custom_logger=logger, log_level=logging.ERROR, use_exception=True)
            return False, f"该配置不可用!原因是: {str(register_ex)}"

    def delete_invalid_configs(self)->int:
        """删除所有无效配置记录 --- 清理无效按钮"""
        def _delete(cur: sqlite3.Cursor)->int:
            # 删除无效的配置项约束记录
            cur.execute("""
                DELETE FROM config_unique_constraints WHERE config_file_record_id NOT IN 
                (SELECT id FROM config_file_record);
            """)
            # 清理失效的配置项约束记录
            cur.execute("""
                DELETE FROM config_unique_constraints WHERE config_file_record_id IN 
                (SELECT id FROM config_file_record WHERE is_active = 0);
            """)
            # 删除失效的配置记录
            cur.execute("DELETE FROM config_file_record WHERE is_active = 0;")
            cur_deleted_count = cur.rowcount
            logger.info(f"deleted {cur.rowcount} invalid config records")
            return cur_deleted_count

        try:
            self._release_validate_cleanup_invalid_records()
            return self._exec_retry(_delete)
        except Exception as deleted_ex:
            logger.exception(f"error deleting invalid configs!")
            raise ValueError(f"删除无效配置失败!原因是: {str(deleted_ex)}")

    def _cleanup_current_process(self):
        """当前进程结束时清理相关资源"""
        try:
            with self._config_lock:
                if not self._is_registered:
                    return

                server_utils.logger_print(msg=f"{self.__class__.__name__} cleaning up current process resources",custom_logger=logger,log_level=logging.INFO)

                # 停止心跳线程
                self._stop_heartbeat_thread()

                # 注销配置
                if self._registered_config_path:
                    self.unregister_config()

                server_utils.logger_print(msg=f"{self.__class__.__name__} cleanup completed",custom_logger=logger,log_level=logging.INFO)

        except Exception: # noqa
            server_utils.logger_print(msg=f"{self.__class__.__name__} cleanup failed",custom_logger=logger,log_level=logging.ERROR,use_exception=True)

    def is_config_registered_in_current_process(self) -> bool:
        """检查当前进程是否已注册配置:外部用"""
        with self._config_lock:
            return self._is_registered

    def get_current_process_config_path(self) -> Optional[str]:
        """获取当前进程注册的配置文件路径:外部用"""
        with self._config_lock:
            return self._registered_config_path

    def refresh_config_status(self)->Tuple[List[Dict],List[Dict],List[Dict]]:
        """刷新所有配置状态获取最新的配置记录"""
        def _refresh(cur: sqlite3.Cursor):
            return self._get_all_config_records_to_by_status(cur)
        # 不需要异常处理,如果出现了异常直接抛出前端界面显示异常原因
        self._release_validate_cleanup_invalid_records()
        return self._exec_retry(_refresh)
    def __del__(self):
        """析构函数，确保资源清理"""
        try:
            self._cleanup_current_process()
        except Exception:  # noqa
            pass  # 忽略析构时的异常
