# 进程系统监控
import logging
import os
import threading
import time

import psutil

from utils import server_utils, shutdown_exec_funct

logger = logging.getLogger(__name__)

class SystemMonitor:
    """
    一个用于在后台线程中监控当前进程CPU和内存使用情况的插件。

    该类的实例会启动一个守护线程，该线程会随着主进程的退出而自动结束。
    监控到的数据是线程安全的，可以从其他任何地方通过 get_usage() 方法获取。

    使用方法:
    1. 在你的主程序中创建该类的一个实例。
    2. 调用 start() 方法启动监控。
    3. 在需要的地方调用 get_usage() 获取实时数据。

    示例:
        monitor = SystemMonitor(interval=1)
        monitor.start()
        # ... 在程序的其他地方 ...
        pid,cpu, mem = monitor.get_usage()
        print(f"当前CPU利用率: {cpu:.2f}%, 内存使用: {mem / 1024 / 1024:.2f} MB")
        monitor.stop() # 停止监控（可选）
    """
    _instance = None
    _instance_lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        """
        使用单例模式，确保在整个应用中只有一个监控实例。
        """
        with cls._instance_lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, interval=1):
        """
        初始化监控器。

        :param interval: int, 监控数据更新的间隔时间（秒）。
        """
        # 防止重复初始化
        self.interval = None
        if getattr(self, '_initialized',False):
            # 如果传入的新 interval 与已有不同, 更新它
            if hasattr(self,'interval') and self.interval is not None and self.interval != interval:
                self.interval = interval
            return
        self._initialized = True

        self.interval = interval
        self.pid = os.getpid()
        self.process = psutil.Process(self.pid)

        # 初始化用于存储监控数据的变量
        self._cpu_percent = 0.0
        self._memory_usage = 0.0

        # 创建一个锁来确保对共享数据的线程安全访问
        self._data_lock = threading.Lock()

        # _stop_event 标志用于优雅地停止线程
        self._stop_event = threading.Event()

        self._monitor_thread = None

        # 预热 cpu_percent，第一次调用cpu_percent(interval=None)会立即返回0.0或一个无意义的值
        # 我们让它在后台开始计算
        try:
             self.process.cpu_percent(interval=None)
        except Exception: # noqa
            pass

        # 系统关闭时自动停止监控线程
        shutdown_exec_funct.register(self.stop)

    def _monitor_loop(self):
        """
        后台循环，持续获取CPU和内存使用情况。
        """
        while not self._stop_event.is_set():
            try:
                # 非阻塞获取自上次调用以来的 CPU 利用率
                cpu_percent = self.process.cpu_percent(interval=None)

                # 获取内存使用情况。rss (Resident Set Size) 是进程在RAM中实际占用的物理内存。
                mem = self.process.memory_info().rss

                # 使用锁来安全地更新共享数据
                with self._data_lock:
                    self._cpu_percent = cpu_percent
                    self._memory_usage = mem

            except psutil.NoSuchProcess:
                # 如果进程在监控期间被杀死，则退出循环
                break
            except Exception as e:
                # 在实际应用中，这里最好使用日志记录错误
                server_utils.logger_print(msg="监控线程出错", custom_logger=logger,log_level= logging.ERROR, use_exception=True, exception=e)
                time.sleep(self.interval)
            # 非阻塞等待
            if self._stop_event.wait(self.interval):
                break


    def start(self):
        """启动监控线程，可多次调用以更新间隔"""
        if self._monitor_thread and self._monitor_thread.is_alive():
            return

        self._monitor_thread = threading.Thread(target=self._monitor_loop, name="SystemMonitorThread",daemon=True)
        self._stop_event.clear()
        self._monitor_thread.start()
        server_utils.logger_print(msg=f"[{self.__class__.__name__}] 监控模块已启动，目标进程PID: {self.pid}", custom_logger=logger,log_level= logging.INFO)


    def stop(self):
        """
        停止后台监控线程（可选，因为守护线程会自动退出）。
        """
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._stop_event.set()
            self._monitor_thread.join(timeout=self.interval+1)
            print(f"[{self.__class__.__name__}] 监控模块已停止。")

    def get_usage(self):
        """
        线程安全地获取最新的CPU和内存使用情况。
        :return: tuple (float, int), 分别是进程pid、CPU利用率（百分比）和内存使用量（字节）。
        """
        with self._data_lock:
            return self.pid,self._cpu_percent, self._memory_usage
