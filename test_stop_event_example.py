#!/usr/bin/env python3
"""
测试 run_server_with_path 函数的 stop_event 功能示例
"""

import asyncio
import sys
import os
import time
from pathlib import Path

# 添加 src 目录到路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from models.webhook_server import run_server_with_path
from utils import server_utils


async def test_stop_event():
    """测试使用外部事件停止服务器"""
    
    # 创建停止事件
    stop_event = asyncio.Event()
    
    # 配置文件路径（需要根据实际情况调整）
    config_path = "config/webhook_server_config.json"
    
    print("启动服务器...")
    
    # 创建服务器任务
    server_task = asyncio.create_task(
        run_server_with_path(config_path, stop_event)
    )
    
    # 创建定时停止任务
    async def stop_after_delay():
        print("等待 10 秒后停止服务器...")
        await asyncio.sleep(10)
        print("触发停止事件...")
        stop_event.set()
        print("停止事件已触发")
    
    stop_task = asyncio.create_task(stop_after_delay())
    
    try:
        # 等待服务器任务完成
        await asyncio.gather(server_task, stop_task)
        print("服务器已成功停止")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")


async def test_without_stop_event():
    """测试不使用外部事件（保持原有行为）"""
    
    config_path = "config/webhook_server_config.json"
    
    print("启动服务器（无外部停止事件）...")
    
    try:
        # 不传入 stop_event，保持原有行为
        await run_server_with_path(config_path)
    except KeyboardInterrupt:
        print("服务器被用户中断")
    except Exception as e:
        print(f"服务器运行出错: {e}")


if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 测试外部停止事件功能")
    print("2. 测试原有行为（无外部停止事件）")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        print("开始测试外部停止事件功能...")
        asyncio.run(test_stop_event())
    elif choice == "2":
        print("开始测试原有行为...")
        asyncio.run(test_without_stop_event())
    else:
        print("无效选择")
        sys.exit(1)
