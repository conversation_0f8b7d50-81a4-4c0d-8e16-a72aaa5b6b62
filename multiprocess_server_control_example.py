#!/usr/bin/env python3
"""
多进程环境中使用 stop_event 控制服务器的示例
"""

import asyncio
import multiprocessing
import sys
import time
from pathlib import Path
from typing import Optional

# 添加 src 目录到路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from models.webhook_server import run_server_with_path


class ServerController:
    """服务器控制器，用于管理服务器的启动和停止"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.server_process: Optional[multiprocessing.Process] = None
        self._stop_event = None
        
    def start_server(self):
        """启动服务器进程"""
        if self.server_process and self.server_process.is_alive():
            print("服务器已经在运行中")
            return
            
        print(f"启动服务器，配置文件: {self.config_path}")
        
        # 创建进程
        self.server_process = multiprocessing.Process(
            target=self._run_server_process,
            args=(self.config_path,),
            daemon=True
        )
        
        self.server_process.start()
        print(f"服务器进程已启动，PID: {self.server_process.pid}")
        
    def stop_server(self):
        """停止服务器进程"""
        if not self.server_process or not self.server_process.is_alive():
            print("没有运行中的服务器进程")
            return
            
        print("正在停止服务器...")
        
        # 终止进程
        self.server_process.terminate()
        
        # 等待进程结束
        try:
            self.server_process.join(timeout=10)
            if self.server_process.is_alive():
                print("强制杀死服务器进程")
                self.server_process.kill()
                self.server_process.join()
        except Exception as e:
            print(f"停止服务器时出错: {e}")
            
        print("服务器已停止")
        self.server_process = None
        
    def is_running(self) -> bool:
        """检查服务器是否在运行"""
        return self.server_process is not None and self.server_process.is_alive()
        
    @staticmethod
    def _run_server_process(config_path: str):
        """在子进程中运行服务器"""
        try:
            # 在子进程中运行异步服务器
            asyncio.run(run_server_with_path(config_path))
        except Exception as e:
            print(f"服务器进程出错: {e}")
            sys.exit(1)


class AsyncServerController:
    """异步服务器控制器，使用 asyncio.Event 进行控制"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.stop_event: Optional[asyncio.Event] = None
        self.server_task: Optional[asyncio.Task] = None
        
    async def start_server(self):
        """启动服务器"""
        if self.server_task and not self.server_task.done():
            print("服务器已经在运行中")
            return
            
        print(f"启动异步服务器，配置文件: {self.config_path}")
        
        # 创建停止事件
        self.stop_event = asyncio.Event()
        
        # 创建服务器任务
        self.server_task = asyncio.create_task(
            run_server_with_path(self.config_path, self.stop_event)
        )
        
        print("异步服务器已启动")
        
    async def stop_server(self):
        """停止服务器"""
        if not self.server_task or self.server_task.done():
            print("没有运行中的服务器")
            return
            
        print("正在停止异步服务器...")
        
        # 触发停止事件
        if self.stop_event:
            self.stop_event.set()
            
        # 等待服务器任务完成
        try:
            await asyncio.wait_for(self.server_task, timeout=10.0)
        except asyncio.TimeoutError:
            print("服务器停止超时，取消任务")
            self.server_task.cancel()
            try:
                await self.server_task
            except asyncio.CancelledError:
                pass
                
        print("异步服务器已停止")
        
    def is_running(self) -> bool:
        """检查服务器是否在运行"""
        return self.server_task is not None and not self.server_task.done()


async def demo_async_control():
    """演示异步控制"""
    config_path = "config/webhook_server_config.json"
    controller = AsyncServerController(config_path)
    
    try:
        # 启动服务器
        await controller.start_server()
        
        # 运行 15 秒
        print("服务器将运行 15 秒...")
        await asyncio.sleep(15)
        
        # 停止服务器
        await controller.stop_server()
        
    except Exception as e:
        print(f"演示过程中出错: {e}")


def demo_multiprocess_control():
    """演示多进程控制"""
    config_path = "config/webhook_server_config.json"
    controller = ServerController(config_path)
    
    try:
        # 启动服务器
        controller.start_server()
        
        # 运行 15 秒
        print("服务器将运行 15 秒...")
        time.sleep(15)
        
        # 停止服务器
        controller.stop_server()
        
    except Exception as e:
        print(f"演示过程中出错: {e}")


if __name__ == "__main__":
    print("选择演示模式:")
    print("1. 异步控制演示")
    print("2. 多进程控制演示")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        print("开始异步控制演示...")
        asyncio.run(demo_async_control())
    elif choice == "2":
        print("开始多进程控制演示...")
        demo_multiprocess_control()
    else:
        print("无效选择")
        sys.exit(1)
